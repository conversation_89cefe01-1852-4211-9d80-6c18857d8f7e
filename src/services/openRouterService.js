const fetch = require('node-fetch');

class OpenRouterService {
  constructor() {
    // Initialize with fallback values until config is loaded
    this.apiKey = process.env.OPENROUTER_API_KEY || '';
    this.baseUrl = 'https://openrouter.ai/api/v1';
    this.model = 'deepseek/deepseek-chat-v3-0324:free';
    this.isConfigured = !!this.apiKey;

    // Initialize configuration
    this.initializeConfig();
  }

  /**
   * Initialize configuration asynchronously
   */
  async initializeConfig() {
    try {
      const configManager = require('../config');

      // Try to get config, but don't fail if it's not ready yet
      if (configManager.isInitialized) {
        const config = configManager.getConfig();
        this.updateConfiguration(config);
      } else {
        // Config will be updated later when it's ready
        console.log('🤖 OpenRouterService: Using fallback values until configuration is loaded');
        this.checkConfiguration();
      }
    } catch (error) {
      console.log('🤖 OpenRouterService: Using environment variables for configuration');
      this.checkConfiguration();
    }
  }

  /**
   * Update configuration when it becomes available
   */
  updateConfiguration(config) {
    if (config && config.openRouter) {
      this.apiKey = config.openRouter.apiKey || process.env.OPENROUTER_API_KEY || '';
      this.baseUrl = config.openRouter.baseUrl || 'https://openrouter.ai/api/v1';
      this.model = config.openRouter.model || 'deepseek/deepseek-chat-v3-0324:free';
      this.isConfigured = !!this.apiKey;

      // console.log('🤖 OpenRouterService Configuration Updated:');
      // console.log(`   Base URL: ${this.baseUrl}`);
      // console.log(`   Model: ${this.model}`);
      // console.log(`   API Key: ${this.apiKey ? '✅ Available' : '❌ REQUIRED'}`);

      if (!this.isConfigured) {
        // console.warn('⚠️  OPENROUTER_API_KEY not configured. Chat functionality will be disabled.');
      } else {
        // console.log('✅ OpenRouterService configured successfully');
      }
    }
  }

  /**
   * Check initial configuration
   */
  checkConfiguration() {
    // console.log('🤖 OpenRouterService Configuration:');
    // console.log(`   Base URL: ${this.baseUrl}`);
    // console.log(`   Model: ${this.model}`);
    // console.log(`   API Key: ${this.apiKey ? '✅ Available' : '❌ REQUIRED'}`);

    if (!this.isConfigured) {
      // console.warn('⚠️  OPENROUTER_API_KEY not configured. Chat functionality will be disabled.');
    } else {
      // console.log('✅ OpenRouterService initialized successfully');
    }
  }

  /**
   * Check if service is configured
   */
  checkConfigurationForOperation() {
    if (!this.isConfigured) {
      throw new Error('OpenRouter service is not configured. Please set OPENROUTER_API_KEY.');
    }
  }

  /**
   * Generate chat response with context
   * @param {string} query - User query (may be refined)
   * @param {string} context - Retrieved context from documents
   * @param {Array} chatHistory - Previous chat messages (optional)
   * @param {Object} refinementInfo - Information about query refinement (optional)
   * @param {Object} options - Additional options (model, temperature, maxTokens, systemPrompt, etc.)
   * @returns {Promise<string>} Generated response
   */
  async generateResponse(query, context = '', chatHistory = [], refinementInfo = null, options = {}) {
    this.checkConfigurationForOperation();

    try {
      // console.log(`🤖 Generating response for query: "${query.substring(0, 50)}..."`);

      // Incorporate additional context if provided
      let enhancedContext = context;
      if (options.context && typeof options.context === 'string') {
        enhancedContext = `${context}\n\nAdditional Context: ${options.context}`;
      }

      const systemPrompt = this.buildSystemPrompt(enhancedContext, options.systemPrompt);
      const messages = this.buildMessages(systemPrompt, enhancedContext, query, chatHistory);

      // 🔍 LOG THE COMPLETE PROMPT AND CONTEXT BEING SENT TO OPENROUTER
      console.log('\n🔍 ═══════════════════════════════════════════════════════════════');
      console.log('🔍 OPENROUTER REQUEST DETAILS');
      console.log('🔍 ═══════════════════════════════════════════════════════════════');
      console.log(`🤖 Model: ${this.model}`);

      // Show semantic refinement information if available
      if (refinementInfo) {
        console.log(`📝 Original Query: "${refinementInfo.originalQuery || 'N/A'}"`);
        console.log(`🧠 Refined Query: "${query}"`);
        console.log(`🔧 Refinements Applied: ${refinementInfo.refinementsCount || 0}`);
        console.log(`⚡ Processing Time: ${refinementInfo.processingTime || 0}ms`);
        if (refinementInfo.refinements && refinementInfo.refinements.length > 0) {
          console.log(`🎯 Refinement Types: ${refinementInfo.refinements.map(r => r.type).join(', ')}`);
        }
      } else {
        console.log(`📝 User Query: "${query}" (no semantic refinement applied)`);
      }

      console.log(`📄 Context Length: ${context.length} characters`);
      console.log(`💬 Chat History: ${chatHistory.length} messages`);
      console.log('\n📋 SYSTEM PROMPT:');
      console.log('─'.repeat(80));
      console.log(systemPrompt);
      console.log('─'.repeat(80));
      console.log('\n📨 COMPLETE MESSAGES ARRAY:');
      console.log(JSON.stringify(messages, null, 2));
      console.log('\n🔍 ═══════════════════════════════════════════════════════════════\n');

      // Use provided options or fall back to defaults
      const model = options.model || this.model;
      const temperature = options.temperature !== undefined ? options.temperature : 0.7;
      const maxTokens = options.maxTokens || 2000;

      const payload = {
        model: model,
        messages,
        temperature: temperature,
        max_tokens: maxTokens,
        stream: false
      };

      const response = await fetch(`${this.baseUrl}/chat/completions`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json',
          'HTTP-Referer': 'https://chatai-sdk.com',
          'X-Title': 'ChatAI SDK Service'
        },
        body: JSON.stringify(payload),
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error(`❌ OpenRouter error: ${response.status} - ${errorText}`);
        throw new Error(`OpenRouter API failed: ${response.status}`);
      }

      const result = await response.json();
      const generatedResponse = result.choices?.[0]?.message?.content || 'No response generated';

      console.log(`✅ Generated response (${generatedResponse.length} chars)`);
      return generatedResponse;

    } catch (error) {
      console.error(`❌ OpenRouter generateResponse error:`, error.message);
      throw error;
    }
  }

  /**
   * Generate streaming chat response
   * @param {string} query - User query (may be refined)
   * @param {string} context - Retrieved context from documents
   * @param {Array} chatHistory - Previous chat messages (optional)
   * @param {Object} refinementInfo - Information about query refinement (optional)
   * @param {Object} options - Additional options (model, temperature, maxTokens, systemPrompt, etc.)
   * @returns {AsyncGenerator<string>} Streaming response chunks
   */
  async* generateStreamingResponse(query, context = '', chatHistory = [], refinementInfo = null, options = {}) {
    this.checkConfigurationForOperation();

    try {
      console.log(`🤖 Generating streaming response for query: "${query.substring(0, 50)}..."`);

      // Log semantic refinement information for streaming
      if (refinementInfo) {
        console.log(`🧠 SEMANTIC REFINEMENT INFO FOR STREAMING:`);
        console.log(`   📝 Original: "${refinementInfo.originalQuery || 'N/A'}"`);
        console.log(`   🎯 Refined: "${query}"`);
        console.log(`   🔧 Refinements: ${refinementInfo.refinementsCount || 0}`);
        console.log(`   ⚡ Time: ${refinementInfo.processingTime || 0}ms`);
      }

      // Incorporate additional context if provided
      let enhancedContext = context;
      if (options.context && typeof options.context === 'string') {
        enhancedContext = `${context}\n\nAdditional Context: ${options.context}`;
      }

      const systemPrompt = this.buildSystemPrompt(enhancedContext, options.systemPrompt);
      const messages = this.buildMessages(systemPrompt, enhancedContext, query, chatHistory);

      // Use provided options or fall back to defaults
      const model = options.model || this.model;
      const temperature = options.temperature !== undefined ? options.temperature : 0.7;
      const maxTokens = options.maxTokens || 2000;

      const payload = {
        model: model,
        messages,
        temperature: temperature,
        max_tokens: maxTokens,
        stream: true
      };

      const apiCallStartTime = Date.now();
      console.log(`⏱️ [TIMING] OpenRouter API request sent at: ${new Date().toISOString()}`);

      const response = await fetch(`${this.baseUrl}/chat/completions`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json',
          'HTTP-Referer': 'https://chatai-sdk.com',
          'X-Title': 'ChatAI SDK Service'
        },
        body: JSON.stringify(payload),
      });

      const apiResponseTime = Date.now();
      const apiResponseDuration = apiResponseTime - apiCallStartTime;
      console.log(`⏱️ [TIMING] OpenRouter API response received: ${apiResponseDuration}ms`);

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`OpenRouter API failed: ${response.status} - ${errorText}`);
      }

      // Parse streaming response using Node.js streams
      let buffer = '';
      let firstChunkReceived = false;

      for await (const chunk of response.body) {
        buffer += chunk.toString();
        const lines = buffer.split('\n');

        // Keep the last incomplete line in buffer
        buffer = lines.pop() || '';

        for (const line of lines) {
          if (line.trim().startsWith('data: ')) {
            const data = line.trim().slice(6);
            if (data === '[DONE]') return;

            try {
              const parsed = JSON.parse(data);
              const content = parsed.choices?.[0]?.delta?.content;
              if (content) {
                // Log first content chunk timing
                if (!firstChunkReceived) {
                  const firstChunkTime = Date.now();
                  const firstChunkDuration = firstChunkTime - apiCallStartTime;
                  console.log(`⏱️ [TIMING] OpenRouter first content chunk: ${firstChunkDuration}ms`);
                  firstChunkReceived = true;
                }
                yield content;
              }
            } catch (parseError) {
              // Skip invalid JSON
            }
          }
        }
      }

    } catch (error) {
      console.error(`❌ OpenRouter streaming error:`, error.message);
      throw error;
    }
  }

  /**
   * Build system prompt with context
   * @param {string} context - Retrieved context information
   * @param {string} customSystemPrompt - Custom system prompt from database (optional)
   * @returns {string} System prompt
   */
  buildSystemPrompt(context, customSystemPrompt = null) {
    // If a custom system prompt is provided, use it as the base
    if (customSystemPrompt && typeof customSystemPrompt === 'string' && customSystemPrompt.trim() !== '') {
      // Combine custom system prompt with important instructions
      return `${customSystemPrompt}

Instructions:
1. CAREFULLY analyze the user's query and match it with the relevant information provided in the context
2. Extract and use ONLY the information from the provided context that directly relates to the user's question
3. If the user's query matches information in the context, provide a comprehensive answer using that specific information
4. If there are validation errors mentioned in the user query, acknowledge them appropriately and guide the user to rephrase their question in a clearer way
5. Do NOT ignore validation errors - address them professionally and help the user understand how to ask their question better
6. Be conversational, friendly, and apologetic when you can't find requested information in the provided context
7. When information isn't available in the provided context, then simply reply "Apologies, I don't currently have information on that topic. Please ask about supported subjects or contact support if needed"
8. Never mention "documents", "provided context", "knowledge base", or "shared content" - respond as if the information is part of your knowledge
9. Use phrases like "Based on my information" or "From what I know" instead of referencing external sources
10. Focus on providing accurate, specific details that directly answer the user's question using the available information
11. Respond as if you're having a natural conversation with the user
12. IMPORTANT: Do NOT use markdown formatting, asterisks, or special formatting in your responses
13. Do NOT include "User:" or "Assistant:" prefixes in your responses
14. Respond directly and naturally without any formatting markers`;
    }

    // Fallback to default system prompt if no custom prompt is provided
    if (!context || typeof context !== 'string' || context.trim() === '') {
      return `You are a helpful AI assistant. Please provide accurate and helpful responses to user questions.`;
    }

    return `You are a helpful AI assistant. Based on my information, I can help answer questions and provide insights.

Instructions:
1. Answer user questions based on the information available to you
2. Be conversational, friendly, and apologetic when you can't find requested information
3. When information isn't available or out of context, then simply reply "Apologies, I don’t currently have information on that topic. Please ask about supported subjects or contact support if needed"
4. Never mention "documents", "provided context", or "shared content" - respond as if the information is part of your knowledge
5. Use phrases like "Based on my information" or "From what I know" instead of referencing documents
6. Respond as if you're having a natural conversation with the user
7. IMPORTANT: Do NOT use markdown formatting, asterisks, or special formatting in your responses
8. Do NOT include "User:" or "Assistant:" prefixes in your responses
9. Respond directly and naturally without any formatting markers`;
  }

  /**
   * Build enhanced system prompt with context quality analysis
   * @param {string} context - Retrieved context information
   * @param {Object} contextMetadata - Context quality metadata
   * @returns {string} Enhanced system prompt
   */
  buildEnhancedSystemPrompt(context, contextMetadata = {}) {
    if (!context || typeof context !== 'string' || context.trim() === '') {
      return `You are a knowledgeable AI assistant. I'm here to help answer your questions and provide insights. Please feel free to ask me anything, and I'll do my best to provide accurate and helpful information.`;
    }

    const contextQuality = contextMetadata.quality || 'GOOD';
    const qualityInstructions = this.getQualityBasedInstructions(contextQuality);

    return `You are an expert AI assistant with access to specialized knowledge. I can provide detailed, accurate answers based on my comprehensive information base.


COMMUNICATION STYLE:
• Be conversational, professional, and engaging
• Provide specific details and examples when available
• Structure complex answers with clear organization
• Use natural language without referencing "documents" or "context"
• When uncertain, acknowledge limitations honestly
• Offer to clarify or expand on any topic

QUALITY STANDARDS:
• Prioritize accuracy over completeness
• Cite specific facts and figures when available
• Explain complex concepts in accessible terms
• Provide actionable insights when relevant
• Maintain consistency with the knowledge base

CRITICAL CONSTRAINT:
• ONLY answer questions that can be addressed using the provided knowledge base
• If a question cannot be answered from the available information, respond with: "I apologize, but I don't have information about that topic in my current knowledge base. Please ask questions related to the provided content."
• DO NOT use general knowledge or training data to answer questions outside the knowledge base
• Stay strictly within the bounds of the provided information

Remember: Respond as if this information is part of your trained knowledge, not external documents.

FORMATTING REQUIREMENTS:
• Do NOT use markdown formatting, asterisks, or special formatting in your responses
• Do NOT include "User:" or "Assistant:" prefixes in your responses
• Respond directly and naturally without any formatting markers
• Use plain text only for all responses`;
  }

  /**
   * Generate quality-based instructions for different context scenarios
   */
  getQualityBasedInstructions(quality) {
    switch (quality) {
      case 'EXCELLENT':
        return `• I have high-quality, highly relevant information to answer your questions
• Provide comprehensive, detailed responses with confidence
• Include specific examples, data points, and nuanced explanations
• Feel free to elaborate on related topics that might be helpful`;

      case 'GOOD':
        return `• I have good information that should address your questions well
• Provide thorough responses while noting any limitations
• Focus on the most relevant and reliable information available
• Supplement with logical reasoning when appropriate`;

      case 'FAIR':
        return `• I have some relevant information, though it may be limited
• Provide what information is available while being transparent about gaps
• Focus on the most reliable aspects of the available information
• Suggest areas where additional clarification might be helpful`;

      case 'POOR':
        return `• My information on this topic appears limited
• Provide what relevant information is available
• Be transparent about limitations and uncertainty
• Suggest that the user might need to consult additional sources`;

      default:
        return `• Provide helpful responses based on available information
• Be clear about the scope and limitations of my knowledge
• Focus on accuracy and relevance in all responses`;
    }
  }

  /**
   * Build messages array for chat completion
   * @param {string} systemPrompt - System prompt (output of buildSystemPrompt function)
   * @param {string} context - vector db context
   * @param {string} query - User query
   * @param {Array} chatHistory - Previous chat messages
   * @returns {Array} Messages array
   */
  buildMessages(systemPrompt, context, query, chatHistory = []) {
    const messages = [
      { role: 'system', content: systemPrompt },
      { role: 'user', content: context },
      { role: 'user', content: query }
    ];

    // Add chat history (limited to last 4 messages = 2 conversations to stay within token limits)
    const recentHistory = chatHistory.slice(-4);
    // Insert chat history before the current query (between context and query)
    messages.splice(2, 0, ...recentHistory);

    // console.log(`💬 Built message array: 1 system + 1 context + ${recentHistory.length} history + 1 current = ${messages.length} total messages`);

    return messages;
  }
}

module.exports = new OpenRouterService();
