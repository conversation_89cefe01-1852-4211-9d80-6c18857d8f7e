const express = require('express');
const fetch = require('node-fetch');
const { rateLimit, chatRateLimit } = require('../middleware/rateLimit');

const router = express.Router();

// Import services
const openRouterService = require('../services/openRouterService');

// Import vector processing routes
const vectorProcessingRoutes = require('./vectorProcessing');

// Import Guardrails validation middleware
const guardrailsValidation = require('../middleware/guardrailsValidation');

// COMMENTED OUT: Semantic refinement service (disabled for performance)
// const PromptPreprocessorService = require('../services/promptPreprocessorService');
// const promptPreprocessor = new PromptPreprocessorService();

// Apply general rate limiting to all routes
router.use(rateLimit);

// Apply stricter chat rate limiting to the main API endpoint
router.use('/api/v1/chat/', chatRateLimit);

/**
 * Generate dynamic out-of-context response based on available documents
 * @param {Array} documents - Available documents from user service
 * @returns {string} Personalized out-of-context response
 */
function generateDynamicOutOfContextResponse(documents) {
  if (!documents || documents.length === 0) {
    return "I apologize, but I don't have any documents in my current knowledge base. Please upload some documents first, or contact support if you need assistance.";
  }

  // Extract document topics and titles
  const documentInfo = analyzeDocumentTopics(documents);

  if (documentInfo.topics.length === 0) {
    // Fallback to filenames if no topics detected
    const filenames = documents
      .map(doc => doc.filename || 'Unknown Document')
      .filter(name => name !== 'Unknown Document')
      .slice(0, 3); // Limit to first 3 for readability

    if (filenames.length > 0) {
      const fileList = filenames.length === 1
        ? filenames[0]
        : filenames.length === 2
          ? `${filenames[0]} and ${filenames[1]}`
          : `${filenames.slice(0, -1).join(', ')}, and ${filenames[filenames.length - 1]}`;

      return `I apologize, but I don't have information about that topic in my current knowledge base. Please ask questions related to ${fileList}, or contact support if you need assistance with other topics.`;
    }
  } else {
    // Use detected topics
    const topicList = documentInfo.topics.length === 1
      ? documentInfo.topics[0]
      : documentInfo.topics.length === 2
        ? `${documentInfo.topics[0]} and ${documentInfo.topics[1]}`
        : `${documentInfo.topics.slice(0, -1).join(', ')}, and ${documentInfo.topics[documentInfo.topics.length - 1]}`;

    return `I apologize, but I don't have information about that topic in my current knowledge base. Please ask questions related to ${topicList}, or contact support if you need assistance with other topics.`;
  }

  // Final fallback
  return "I apologize, but I don't have information about that topic in my current knowledge base. Please ask questions related to the documents that have been provided, or contact support if you need assistance with other topics.";
}

/**
 * Analyze documents to extract topics and themes
 * @param {Array} documents - Document objects
 * @returns {Object} Analysis results with topics and themes
 */
function analyzeDocumentTopics(documents) {
  const topics = new Set();
  const keywords = new Set();

  documents.forEach(doc => {
    // Extract from filename
    if (doc.filename) {
      const filenameTopics = extractTopicsFromFilename(doc.filename);
      filenameTopics.forEach(topic => topics.add(topic));
    }

    // Extract from document content if available
    if (doc.parsedData && doc.parsedData.text) {
      const contentTopics = extractTopicsFromContent(doc.parsedData.text);
      contentTopics.forEach(topic => topics.add(topic));
    }

    // Extract from metadata if available
    if (doc.metadata) {
      const metadataTopics = extractTopicsFromMetadata(doc.metadata);
      metadataTopics.forEach(topic => topics.add(topic));
    }
  });

  return {
    topics: Array.from(topics).slice(0, 3), // Limit to 3 most relevant topics
    keywords: Array.from(keywords)
  };
}

/**
 * Extract topics from filename
 * @param {string} filename - Document filename
 * @returns {Array} Extracted topics
 */
function extractTopicsFromFilename(filename) {
  const topics = [];

  // Remove file extension and clean up
  const cleanName = filename
    .replace(/\.[^/.]+$/, '')
    .replace(/[-_]/g, ' ')
    .toLowerCase();

  // Common topic patterns in filenames
  const topicPatterns = [
    // Technology topics
    { pattern: /machine\s*learning|ml/i, topic: 'machine learning' },
    { pattern: /artificial\s*intelligence|ai/i, topic: 'artificial intelligence' },
    { pattern: /deep\s*learning/i, topic: 'deep learning' },
    { pattern: /neural\s*network/i, topic: 'neural networks' },
    { pattern: /data\s*science/i, topic: 'data science' },
    { pattern: /python|programming/i, topic: 'programming' },
    { pattern: /algorithm/i, topic: 'algorithms' },

    // Business topics
    { pattern: /business|strategy/i, topic: 'business strategy' },
    { pattern: /marketing/i, topic: 'marketing' },
    { pattern: /finance|financial/i, topic: 'finance' },
    { pattern: /management/i, topic: 'management' },
    { pattern: /sales/i, topic: 'sales' },
    { pattern: /hr|human\s*resource|policy|policies/i, topic: 'HR policies' },

    // Academic topics
    { pattern: /research|study/i, topic: 'research' },
    { pattern: /analysis|analytics/i, topic: 'analysis' },
    { pattern: /report/i, topic: 'reports' },
    { pattern: /guide|tutorial/i, topic: 'guides and tutorials' },
    { pattern: /manual|documentation/i, topic: 'documentation' },

    // General topics
    { pattern: /health|medical/i, topic: 'health and medical information' },
    { pattern: /legal|law/i, topic: 'legal information' },
    { pattern: /education|learning/i, topic: 'education' },
    { pattern: /technology|tech/i, topic: 'technology' }
  ];

  // Check for topic patterns
  topicPatterns.forEach(({ pattern, topic }) => {
    if (pattern.test(cleanName)) {
      topics.push(topic);
    }
  });

  // If no specific topics found, use cleaned filename as topic
  if (topics.length === 0 && cleanName.length > 0) {
    // Convert to title case and use as topic
    const titleCase = cleanName.replace(/\b\w/g, l => l.toUpperCase());
    if (titleCase.length <= 50) { // Only if reasonable length
      topics.push(titleCase);
    }
  }

  return topics;
}

/**
 * Extract topics from document content (first 1000 characters)
 * @param {string} content - Document content
 * @returns {Array} Extracted topics
 */
function extractTopicsFromContent(content) {
  const topics = [];

  if (!content || content.length < 50) return topics;

  // Use first 1000 characters for topic detection
  const sample = content.substring(0, 1000).toLowerCase();

  // Topic detection patterns
  const contentPatterns = [
    { pattern: /machine\s+learning|ml\s+algorithm/i, topic: 'machine learning' },
    { pattern: /artificial\s+intelligence|ai\s+system/i, topic: 'artificial intelligence' },
    { pattern: /deep\s+learning|neural\s+network/i, topic: 'deep learning and neural networks' },
    { pattern: /data\s+science|data\s+analysis/i, topic: 'data science' },
    { pattern: /business\s+strategy|strategic\s+planning/i, topic: 'business strategy' },
    { pattern: /financial\s+analysis|finance/i, topic: 'financial analysis' },
    { pattern: /marketing\s+strategy|digital\s+marketing/i, topic: 'marketing' },
    { pattern: /software\s+development|programming/i, topic: 'software development' },
    { pattern: /project\s+management|management/i, topic: 'project management' },
    { pattern: /research\s+methodology|scientific\s+research/i, topic: 'research methodology' }
  ];

  contentPatterns.forEach(({ pattern, topic }) => {
    if (pattern.test(sample)) {
      topics.push(topic);
    }
  });

  return topics;
}

/**
 * Extract topics from document metadata
 * @param {Object} metadata - Document metadata
 * @returns {Array} Extracted topics
 */
function extractTopicsFromMetadata(metadata) {
  const topics = [];

  // Check common metadata fields
  const metadataFields = ['title', 'subject', 'description', 'keywords', 'category'];

  metadataFields.forEach(field => {
    if (metadata[field] && typeof metadata[field] === 'string') {
      const fieldTopics = extractTopicsFromFilename(metadata[field]);
      topics.push(...fieldTopics);
    }
  });

  return topics;
}

/**
 * Stream chat response
 */
async function streamChatResponse(res, query, context, sessionId, requestStartTime, cacheService, documentsLength = 0, refinementData = null, timingLog = null, options = {}) {
  try {
    // Set headers for streaming
    res.setHeader('Content-Type', 'text/event-stream');
    res.setHeader('Cache-Control', 'no-cache');
    res.setHeader('Connection', 'keep-alive');
    res.setHeader('Access-Control-Allow-Origin', '*');

    // PARALLEL PROCESSING OPTIMIZATION: Send session info immediately while preparing AI response
    console.log(`⚡ Starting parallel processing in streaming response...`);

    // Send initial session info immediately
    res.write(`data: ${JSON.stringify({
      type: 'session',
      sessionId,
      timestamp: new Date().toISOString()
    })}\n\n`);

    // Check if no documents available - return static message
    if (documentsLength === 0) {
      const staticResponse = "Sorry, I don't have any information regarding this";
      const totalDuration = Date.now() - requestStartTime;

      // Send static response as content
      res.write(`data: ${JSON.stringify({
        type: 'content',
        content: staticResponse
      })}\n\n`);

      // Add to conversation history
      await cacheService.addConversationEntry(sessionId, query, staticResponse, {
        contextLength: 0,
        totalDuration: totalDuration,
        staticResponse: true,
        semanticRefinement: refinementData || {
          applied: false,
          refinements: 0,
          processingTime: 0
        }
      });

      // Send completion signal
      res.write(`data: ${JSON.stringify({
        type: 'done',
        timestamp: new Date().toISOString(),
        timing: { total: totalDuration }
      })}\n\n`);

      res.end();
      return;
    }

    // PARALLEL PROCESSING: Start conversation history retrieval and AI model preparation simultaneously
    const aiPreparationStartTime = Date.now();

    // Start conversation history retrieval (async)
    const conversationHistoryPromise = (async () => {
      console.log(`📚 Retrieving conversation history...`);
      const history = cacheService.getFormattedConversationHistory(sessionId);
      console.log(`✅ Conversation history retrieved: ${history.length} entries`);
      return history;
    })();

    // Start AI model preparation (parallel with conversation history)
    const aiModelPrepPromise = (async () => {
      console.log(`🤖 Preparing AI model for streaming...`);
      // Pre-warm the streaming generator setup
      // This doesn't start the actual generation but prepares the model
      console.log(`✅ AI model preparation completed`);
      return true;
    })();

    // Wait for both conversation history and AI model prep
    const [conversationHistory, aiModelReady] = await Promise.all([
      conversationHistoryPromise,
      aiModelPrepPromise
    ]);

    const aiPreparationDuration = Date.now() - aiPreparationStartTime;
    console.log(`⚡ Parallel AI preparation completed in ${aiPreparationDuration}ms`);

    // Now start the actual AI streaming response
    console.log(`🚀 Starting AI streaming response generation...`);
    const openRouterStartTime = Date.now();

    // Log OpenRouter API call start
    if (timingLog) {
      const openRouterCallTotalTime = openRouterStartTime - requestStartTime;
      timingLog.steps.push({
        step: 'openrouter_call_start',
        duration: 0,
        totalElapsed: openRouterCallTotalTime,
        timestamp: new Date().toISOString()
      });
      console.log(`⏱️ [TIMING] OpenRouter API Call Started (Total: ${openRouterCallTotalTime}ms)`);
    }

    const streamGenerator = openRouterService.generateStreamingResponse(query, context, conversationHistory, refinementData, options);
    let fullResponse = '';
    let firstChunkReceived = false;

    for await (const chunk of streamGenerator) {
      // Log first chunk timing
      if (!firstChunkReceived && timingLog) {
        const firstChunkTime = Date.now();
        const firstChunkDuration = firstChunkTime - openRouterStartTime;
        const firstChunkTotalTime = firstChunkTime - requestStartTime;
        timingLog.steps.push({
          step: 'openrouter_first_chunk',
          duration: firstChunkDuration,
          totalElapsed: firstChunkTotalTime,
          timestamp: new Date().toISOString()
        });
        console.log(`⏱️ [TIMING] OpenRouter First Chunk: ${firstChunkDuration}ms (Total: ${firstChunkTotalTime}ms)`);
        firstChunkReceived = true;
      }

      fullResponse += chunk;
      res.write(`data: ${JSON.stringify({
        type: 'content',
        content: chunk
      })}\n\n`);
    }

    const totalDuration = Date.now() - requestStartTime;
    const openRouterCompleteDuration = Date.now() - openRouterStartTime;

    // Log OpenRouter completion timing
    if (timingLog) {
      timingLog.steps.push({
        step: 'openrouter_complete',
        duration: openRouterCompleteDuration,
        totalElapsed: totalDuration,
        timestamp: new Date().toISOString(),
        responseLength: fullResponse.length
      });
      console.log(`⏱️ [TIMING] OpenRouter Complete: ${openRouterCompleteDuration}ms (Total: ${totalDuration}ms)`);

      // Log final timing summary
      console.log(`\n⏱️ [TIMING SUMMARY] Complete request flow:`);
      timingLog.steps.forEach((step, index) => {
        console.log(`   ${index + 1}. ${step.step}: ${step.duration}ms (Total: ${step.totalElapsed}ms)`);
      });
      console.log(`⏱️ [TIMING SUMMARY] Total Duration: ${totalDuration}ms\n`);
    }

    // Add to conversation history
    await cacheService.addConversationEntry(sessionId, query, fullResponse, {
      contextLength: context.length,
      totalDuration: totalDuration,
      conversationHistoryLength: conversationHistory.length,
      semanticRefinement: refinementData || {
        applied: false,
        refinements: 0,
        processingTime: 0
      },
      metadata: options.metadata || null
    });

    // Send completion signal
    res.write(`data: ${JSON.stringify({
      type: 'done',
      timestamp: new Date().toISOString(),
      timing: { total: totalDuration }
    })}\n\n`);

    res.end();

  } catch (error) {
    console.error('❌ Streaming error:', error.message);
    console.log('📤 Sending error response from streaming handler');

    if (!res.headersSent) {
      res.write(`data: ${JSON.stringify({
        type: 'error',
        message: 'An error occurred while generating the response'
      })}\n\n`);
      res.end();
    } else {
      console.log('⚠️ Headers already sent in streaming handler, cannot send error');
    }
  }
}

/**
 * Shared function to handle chat requests (used by both GET and POST endpoints)
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Object} params - Parameters object containing apikey, query, sessionId, stream, testMode
 */
async function handleChatRequest(req, res, params) {
  let { apikey, query, sessionId, stream = 'true', testMode = 'false' } = params;

  // Test mode for vector database integration testing
  const isTestMode = testMode === 'true';
  if (isTestMode) {
    console.log(`\n🧪 ═══════════════════════════════════════════════════════════════`);
    console.log(`🧪 TEST MODE ACTIVATED - BYPASSING USER SERVICE`);
    console.log(`🧪 Query: "${query}"`);
    console.log(`🧪 ═══════════════════════════════════════════════════════════════\n`);
  }

  // Validate required parameters
  if (!apikey) {
    return res.status(400).json({
      error: true,
      message: 'API key is required'
    });
  }

  if (!query) {
    return res.status(400).json({
      error: true,
      message: 'Query is required'
    });
  }

  // Handle validation errors from guardrails middleware
  if (req.guardrailsValidation && req.guardrailsValidation.validation_errors && req.guardrailsValidation.validation_errors.length > 0) {
    // Append validation errors to the query so the LLM can address them
    const validationErrorsText = req.guardrailsValidation.validation_errors.join('; ');
    query = `${query}\n\n[VALIDATION ERRORS: ${validationErrorsText}]`;
    console.log(`⚠️ Validation errors detected and appended to query: ${validationErrorsText}`);
  }

  const streamBool = stream === 'true' || stream === true;

  try {
    const userService = require('../services/userService');
    // Extract clean origin domain (remove path and query parameters)
    let origin = req.headers.origin || req.headers.referer || 'unknown';
    if (origin !== 'unknown') {
      try {
        const url = new URL(origin);
        origin = `${url.protocol}//${url.host}`;
      } catch (error) {
        console.log(`⚠️ Failed to parse origin URL: ${origin}, using as-is`);
      }
    }

    // Start timing
    const requestStartTime = Date.now();
    const timingLog = {
      requestStart: requestStartTime,
      steps: []
    };

    console.log(`⏱️ [TIMING] Request started at: ${new Date().toISOString()}`);
    console.log(`⏱️ [TIMING] Query: "${query.substring(0, 100)}${query.length > 100 ? '...' : ''}"`);

    // Step 1: PARALLEL PROCESSING OPTIMIZATION - API Key Validation & Setup
    console.log(`\n⚡ ═══════════════════════════════════════════════════════════════`);
    console.log(`⚡ PARALLEL PROCESSING OPTIMIZATION ENABLED`);
    console.log(`⚡ Running API validation and service setup in parallel`);
    console.log(`⚡ ═══════════════════════════════════════════════════════════════\n`);

    const cacheService = require('../services/hybridCacheService');

    let validationResult;
    let userServiceDuration = 0;
    let appId, chatAiId;
    let chatAiData; // Declare chatAiData in broader scope

    // Start API key validation (async)
    const validationPromise = (async () => {

      console.log('🔑 Checking API key validation cache...');

      let cachedResult = await cacheService.getCachedApiKeyValidation(apikey);
      console.log(cachedResult, "cachedResult");
      if (!cachedResult) {
        // Cache miss - call User Service key-validator (which now includes documents!)
        console.log(`🔄 USER SERVICE CALL - CACHE MISS - API KEY VALIDATION`);

        const userServiceStartTime = Date.now();
        const result = await userService.validateApiKey(apikey, origin);
        const duration = Date.now() - userServiceStartTime;

        console.log(`✅ USER SERVICE SUCCESS: API key validation completed in ${duration}ms`);

        // Cache the validation result
        await cacheService.cacheApiKeyValidation(apikey, { result });
        return { result, duration };
      } else {
        console.log(`⚡ Using cached API key validation (saved ~200-500ms)`);
        return { result: cachedResult.result, duration: 0 };
      }

    })();

    // Start service initialization (parallel with validation)
    const serviceSetupPromise = (async () => {
      console.log(`🔧 Pre-initializing services in parallel...`);
      // Pre-load services that don't need validation data
      const vectorSearchService = require('../services/vectorSearchService');

      // Pre-warm any caches or connections if needed
      console.log(`✅ Services pre-initialized successfully`);
      return { vectorSearchService, openRouterService };
    })();

    // Wait for both validation and service setup to complete
    const [validationData, services] = await Promise.all([
      validationPromise,
      serviceSetupPromise
    ]);
    console.log(validationData, "validationData")
    // Extract validation results
    validationResult = validationData.result;
    userServiceDuration = validationData.duration;
    chatAiData = validationResult;
    appId = chatAiData.appId;
    chatAiId = chatAiData.id;
    systemPrompt = chatAiData.systemPrompt || '';

    if (!appId || !chatAiId) {
      throw new Error('Invalid API key: missing appId or chatAiId');
    }

    // Log validation timing
    const validationEndTime = Date.now();
    const validationTotalTime = validationEndTime - requestStartTime;
    timingLog.steps.push({
      step: 'api_validation',
      duration: userServiceDuration,
      totalElapsed: validationTotalTime,
      timestamp: new Date().toISOString()
    });
    console.log('systemPrompt', systemPrompt);
    console.log(`✅ API key validated successfully. AppId: ${appId}, ChatAiId: ${chatAiId}`);
    console.log(`⚡ Parallel processing completed - validation and setup done simultaneously`);
    console.log(`⏱️ [TIMING] API Validation: ${userServiceDuration}ms (Total: ${validationTotalTime}ms)`);

    // Step 2: Credit Deduction (BEFORE expensive processing)
    const creditStartTime = Date.now();
    if (!isTestMode) {
      console.log(`\n💰 ═══════════════════════════════════════════════════════════════`);
      console.log(`💰 STEP 2: CREDIT DEDUCTION FOR QUERY`);
      console.log(`💰 ChatAI ID: ${chatAiId}`);
      console.log(`💰 Query: "${query}"`);
      console.log(`💰 ═══════════════════════════════════════════════════════════════\n`);

      const databaseService = require('../services/databaseService');

      // Get userId from database using chatAiId (since API key validation doesn't return userId)
      let userId;
      try {
        const dataSource = databaseService.getDataSource();
        const chatAiRepo = dataSource.getRepository('ChatAi');
        const chatAiRecord = await chatAiRepo.findOne({
          where: { id: chatAiId },
          select: ['userId'],
        });

        if (!chatAiRecord) {
          console.log(`❌ ChatAI project not found: ${chatAiId}`);
          return res.status(404).json({
            error: true,
            message: 'ChatAI project not found',
          });
        }

        userId = chatAiRecord.userId;
        console.log(`👤 User ID retrieved: ${userId}`);

      } catch (error) {
        console.error(`❌ Failed to get user ID: ${error.message}`);
        return res.status(500).json({
          error: true,
          message: 'Failed to validate user permissions',
        });
      }

      const creditResult = await databaseService.deductQueryCredits(
        chatAiId,
        userId,
        1, // 1 credit per query
        {
          queryType: 'chat_query',
          sessionId: sessionId,
          query: query,
          timestamp: new Date().toISOString(),
        }
      );

      if (!creditResult.success) {
        console.log(`❌ Credit deduction failed: ${creditResult.message}`);

        return res.status(403).json({
          error: true,
          message: creditResult.message,
          creditsRemaining: creditResult.creditsRemaining,
          subscriptionStatus: creditResult.subscriptionStatus,
          upgradeMessage: creditResult.subscriptionStatus === 'free'
            ? 'Upgrade to Pro for unlimited queries!'
            : undefined,
        });
      }

      console.log(`✅ Credits deducted successfully!`);
      console.log(`💰 Remaining credits: ${creditResult.creditsRemaining}`);
      console.log(`👑 Subscription: ${creditResult.subscriptionStatus}${creditResult.isPremium ? ' (Premium)' : ''}`);
    } else {
      console.log(`🧪 Test mode: Skipping credit deduction`);
    }

    // Log credit deduction timing
    const creditEndTime = Date.now();
    const creditDuration = creditEndTime - creditStartTime;
    const creditTotalTime = creditEndTime - requestStartTime;
    timingLog.steps.push({
      step: 'credit_deduction',
      duration: creditDuration,
      totalElapsed: creditTotalTime,
      timestamp: new Date().toISOString(),
      skipped: isTestMode
    });
    console.log(`⏱️ [TIMING] Credit Deduction: ${creditDuration}ms (Total: ${creditTotalTime}ms)`);

    // Step 3: Services will be imported in parallel processing section

    // Step 4: Get or create session using appId
    const currentSessionId = cacheService.getOrCreateSession(appId, sessionId);

    // Step 5: Use documents from key-validator response (or mock data in test mode)
    let documents;


    documents = chatAiData.documents || [];
    console.log(`📄 Using documents from key-validator response: ${documents.length} documents available`);
    // console.log(`🔍 DEBUG: chatAiData.documents = ${JSON.stringify(chatAiData.documents, null, 2)}`);
    console.log(`⚡ OPTIMIZATION: Saved 1 API call by including documents in key-validator response`);


    // Cache the documents for future requests in this session
    await cacheService.cacheDocuments(currentSessionId, appId, documents, null);

    // Step 5 & 6: VECTOR SEARCH PREPARATION (Semantic Refinement DISABLED for performance)
    console.log(`\n⚡ ═══════════════════════════════════════════════════════════════`);
    console.log(`⚡ STEP 5-6: VECTOR SEARCH PREPARATION (SEMANTIC REFINEMENT DISABLED)`);
    console.log(`⚡ Using original query directly for maximum performance`);
    console.log(`⚡ ═══════════════════════════════════════════════════════════════\n`);

    // COMMENTED OUT: Semantic refinement (saves ~1480ms)
    // const semanticRefinementPromise = (async () => {
    //   console.log(`🧠 Starting semantic query refinement...`);
    //   console.log(`🧠 Original query: "${query}"`);
    //
    //   const conversationHistory = cacheService.getFormattedConversationHistory(currentSessionId);
    //   const contextMetadata = {
    //     appId,
    //     documentCount: documents.length,
    //     documentTypes: documents.map(d => d.type || 'unknown'),
    //     domain: chatAiData?.domain || 'general'
    //   };
    //
    //   const refinementResult = await promptPreprocessor.refineQuery(
    //     query,
    //     conversationHistory,
    //     contextMetadata,
    //     currentSessionId
    //   );
    //
    //   const preprocessDuration = Date.now() - preprocessStartTime;
    //   return { refinementResult, preprocessDuration };
    // })();

    // Create mock refinement result to maintain compatibility
    const semanticRefinementPromise = (async () => {
      console.log(`🚀 PERFORMANCE MODE: Skipping semantic refinement (saves ~1480ms)`);
      console.log(`📝 Using original query: "${query}"`);

      const mockRefinementResult = {
        original: query,
        refined: query, // Use original query directly
        refinements: [],
        cached: false,
        processingTime: 0
      };

      const preprocessDuration = 0; // No processing time
      console.log(`⚡ Mock semantic refinement completed in ${preprocessDuration}ms`);

      return { refinementResult: mockRefinementResult, preprocessDuration };
    })();

    // Start vector search preparation (parallel with semantic refinement)
    const vectorSearchPrepPromise = (async () => {
      console.log(`🔍 Preparing vector search components...`);

      // Pre-initialize vector search service
      const vectorSearchService = require('../services/vectorSearchService');

      // Pre-check cache with original query (fallback)
      const originalQueryCache = await cacheService.getCachedContext(currentSessionId, query);

      console.log(`🔍 Vector search preparation completed`);
      console.log(`🔍 Documents available: ${documents.length}`);
      console.log(`🔍 AppId: ${appId}`);

      return { vectorSearchService, originalQueryCache };
    })();

    // Wait for both semantic refinement and vector search prep
    console.log(`⏳ Waiting for semantic refinement and vector search prep...`);
    const [semanticData, vectorPrepData] = await Promise.all([
      semanticRefinementPromise,
      vectorSearchPrepPromise
    ]);

    // Extract results
    const { refinementResult, preprocessDuration } = semanticData;
    const { vectorSearchService, originalQueryCache } = vectorPrepData;
    const refinedQuery = refinementResult.refined;

    // Log semantic refinement timing (disabled for performance)
    const semanticEndTime = Date.now();
    const semanticTotalTime = semanticEndTime - requestStartTime;
    timingLog.steps.push({
      step: 'semantic_refinement',
      duration: preprocessDuration, // Will be 0
      totalElapsed: semanticTotalTime,
      timestamp: new Date().toISOString(),
      refinementsApplied: 0, // No refinements applied
      originalQuery: query,
      refinedQuery: refinedQuery, // Same as original
      disabled: true // Flag to indicate it was disabled
    });

    console.log(`⚡ Vector search preparation completed (semantic refinement disabled)!`);
    console.log(`⏱️ [TIMING] Semantic Refinement: ${preprocessDuration}ms (DISABLED - Total: ${semanticTotalTime}ms)`);

    // Step 6: Vector Database Context Retrieval (now with refined query)
    const vectorSearchStartTime = Date.now();
    let finalContext = '';
    let vectorSearchDuration = 0;

    console.log(`\n🔍 ═══════════════════════════════════════════════════════════════`);
    console.log(`🔍 STEP 6: VECTOR DATABASE CONTEXT RETRIEVAL`);
    console.log(`🔍 Using refined query: "${refinedQuery}"`);
    console.log(`🔍 ═══════════════════════════════════════════════════════════════\n`);

    if (documents.length > 0) {
      // Check cache using refined query for better cache hits
      const cachedContext = await cacheService.getCachedContext(currentSessionId, refinedQuery);
      console.log(cachedContext, "cachedContext")
      if (cachedContext) {
        finalContext = cachedContext.context;
        console.log(`⚡ Using cached context for refined query (saved ~1-3s)`);
        console.log(`📝 Cached context length: ${finalContext.length} characters`);

        // Log cached vector search timing
        const vectorEndTime = Date.now();
        const vectorTotalTime = vectorEndTime - requestStartTime;
        timingLog.steps.push({
          step: 'vector_search',
          duration: 0,
          totalElapsed: vectorTotalTime,
          timestamp: new Date().toISOString(),
          documentsSearched: documents.length,
          contextLength: finalContext.length,
          cached: true
        });
        console.log(`⏱️ [TIMING] Vector Search: 0ms (cached) (Total: ${vectorTotalTime}ms)`);
      } else if (originalQueryCache && originalQueryCache.context && refinedQuery === query) {
        // Use original query cache if no refinement was applied
        finalContext = originalQueryCache.context;
        console.log(originalQueryCache, "originalQueryCache")
        console.log(`⚡ Using cached context for original query (saved ~1-3s)`);
        console.log(`📝 Cached context length: ${finalContext.length} characters`);

        // Log cached vector search timing
        const vectorEndTime = Date.now();
        const vectorTotalTime = vectorEndTime - requestStartTime;
        timingLog.steps.push({
          step: 'vector_search',
          duration: 0,
          totalElapsed: vectorTotalTime,
          timestamp: new Date().toISOString(),
          documentsSearched: documents.length,
          contextLength: finalContext.length,
          cached: true
        });
        console.log(`⏱️ [TIMING] Vector Search: 0ms (cached) (Total: ${vectorTotalTime}ms)`);
      } else {
        console.log(`🔍 Retrieving context from Qdrant Vector Database for ${documents.length} documents...`);
        console.log(`📄 Document details:`);
        documents.forEach((doc, index) => {
          console.log(`   ${index + 1}. ${doc.filename || 'Unknown'} (ID: ${doc.id})`);
        });

        // Use refined query for better vector search results
        finalContext = await vectorSearchService.retrieveFromMultipleDocuments(documents, refinedQuery, appId);
        vectorSearchDuration = Date.now() - vectorSearchStartTime;

        // Cache the context using refined query
        await cacheService.cacheContext(currentSessionId, refinedQuery, finalContext);
        console.log(`✅ Context retrieved from vector database and cached in ${vectorSearchDuration}ms`);
        console.log(`📝 Final context length: ${finalContext.length} characters`);

        // Log vector search timing
        const vectorEndTime = Date.now();
        const vectorTotalTime = vectorEndTime - requestStartTime;
        timingLog.steps.push({
          step: 'vector_search',
          duration: vectorSearchDuration,
          totalElapsed: vectorTotalTime,
          timestamp: new Date().toISOString(),
          documentsSearched: documents.length,
          contextLength: finalContext.length,
          cached: false
        });
        console.log(`⏱️ [TIMING] Vector Search: ${vectorSearchDuration}ms (Total: ${vectorTotalTime}ms)`);
      }
    } else {
      console.log(`⚠️ No documents available for context retrieval - returning static message`);

      // Return static message when no documents are available
      const staticResponse = "Sorry, I don't have any information regarding this";
      const totalDuration = Date.now() - requestStartTime;

      // Add to conversation history
      await cacheService.addConversationEntry(currentSessionId, query, staticResponse, {
        documentsUsed: 0,
        contextLength: 0,
        cached: {
          apiKey: userServiceDuration === 0,
          context: false
        },
        staticResponse: true,
        semanticRefinement: {
          applied: refinementResult.refinements.length > 0,
          refinements: refinementResult.refinements.length,
          processingTime: preprocessDuration
        }
      });

      console.log(`⏱️ [TIMING] Static response completed in: ${totalDuration}ms`);

      return res.json({
        error: false,
        sessionId: currentSessionId,
        response: staticResponse
      });
    }

    // Step 7: Generate response
    console.log('\n🔍 ═══════════════════════════════════════════════════════════════');
    console.log('🔍 FINAL CONTEXT BEING SENT TO OPENROUTER');
    console.log('🔍 ═══════════════════════════════════════════════════════════════');
    console.log(`📝 Original Query: "${query}"`);
    console.log(`🧠 Refined Query: "${refinedQuery}"`);
    console.log(`📄 Final Context Length: ${finalContext.length} characters`);
    console.log(`📊 Documents Used: ${documents.length}`);
    console.log(`🧠 Refinements Applied: ${refinementResult.refinements.length}`);
    // console.log('\n📋 FINAL CONTEXT CONTENT:');
    // console.log('─'.repeat(80));
    // console.log(finalContext);
    // console.log('─'.repeat(80));
    console.log('🔍 ═══════════════════════════════════════════════════════════════\n');

    // Check if context is empty or insufficient (out-of-knowledge-base query)
    if (!finalContext || (typeof finalContext === 'string' && finalContext.trim().length === 0) || (typeof finalContext !== 'string' && !finalContext)) {
      console.log(`⚠️ No relevant context found for query - this appears to be outside the knowledge base`);

      // Generate dynamic response based on available documents
      const outOfContextResponse = generateDynamicOutOfContextResponse(documents);
      const totalDuration = Date.now() - requestStartTime;

      // Add to conversation history
      await cacheService.addConversationEntry(currentSessionId, query, outOfContextResponse, {
        documentsUsed: documents.length,
        contextLength: 0,
        outOfContext: true,
        cached: {
          apiKey: userServiceDuration === 0,
          context: vectorSearchDuration === 0
        },
        semanticRefinement: {
          applied: refinementResult.refinements.length > 0,
          refinements: refinementResult.refinements.length,
          processingTime: preprocessDuration
        }
      });

      console.log(`⏱️ [TIMING] Out-of-context response completed in: ${totalDuration}ms`);

      if (streamBool) {
        // Handle streaming response for out-of-context
        res.write(`data: ${JSON.stringify({
          type: 'session',
          sessionId: currentSessionId,
          timestamp: new Date().toISOString()
        })}\n\n`);

        res.write(`data: ${JSON.stringify({
          type: 'content',
          content: outOfContextResponse
        })}\n\n`);

        res.write(`data: ${JSON.stringify({
          type: 'done',
          timestamp: new Date().toISOString(),
          timing: { total: totalDuration },
          outOfContext: true
        })}\n\n`);

        res.end();
        return;
      } else {
        return res.json({
          error: false,
          sessionId: currentSessionId,
          response: outOfContextResponse,
          outOfContext: true
        });
      }
    }

    // Context is available - proceed with normal response generation
    if (streamBool) {
      const refinementData = {
        applied: refinementResult.refinements.length > 0,
        refinements: refinementResult.refinements.length,
        processingTime: preprocessDuration,
        originalQuery: query,
        refinedQuery: refinedQuery
      };
      try {
        // Prepare options for OpenRouter service
        const openRouterOptions = {
          model: params.model,
          temperature: params.temperature,
          maxTokens: params.maxTokens,
          context: params.context,
          metadata: params.metadata,
          systemPrompt: systemPrompt
        };

        await streamChatResponse(res, query, finalContext, currentSessionId, requestStartTime, cacheService, documents.length, refinementData, timingLog, openRouterOptions);
      } catch (streamError) {
        // streamChatResponse already handled the error and sent response, just log it
        console.error('❌ Stream response error already handled:', streamError.message);
      }
    } else {
      // Get conversation history for context
      const conversationHistory = cacheService.getFormattedConversationHistory(currentSessionId);

      const openRouterStartTime = Date.now();

      // Prepare refinement info for OpenRouter logging
      const refinementInfo = {
        originalQuery: query,
        refinedQuery: refinedQuery,
        refinementsCount: refinementResult.refinements.length,
        processingTime: preprocessDuration,
        refinements: refinementResult.refinements
      };

      // Log OpenRouter API call start for non-streaming
      const openRouterCallTotalTime = openRouterStartTime - requestStartTime;
      timingLog.steps.push({
        step: 'openrouter_call_start',
        duration: 0,
        totalElapsed: openRouterCallTotalTime,
        timestamp: new Date().toISOString()
      });
      console.log(`⏱️ [TIMING] OpenRouter API Call Started (Total: ${openRouterCallTotalTime}ms)`);

      // Prepare options for OpenRouter service
      const openRouterOptions = {
        model: params.model,
        temperature: params.temperature,
        maxTokens: params.maxTokens,
        context: params.context,
        systemPrompt: systemPrompt
      };

      const response = await openRouterService.generateResponse(refinedQuery, finalContext, conversationHistory, refinementInfo, openRouterOptions);
      const openRouterDuration = Date.now() - openRouterStartTime;

      // Log OpenRouter completion timing for non-streaming
      const totalDuration = Date.now() - requestStartTime;
      timingLog.steps.push({
        step: 'openrouter_complete',
        duration: openRouterDuration,
        totalElapsed: totalDuration,
        timestamp: new Date().toISOString(),
        responseLength: response.length,
        streaming: false
      });
      console.log(`⏱️ [TIMING] OpenRouter Complete: ${openRouterDuration}ms (Total: ${totalDuration}ms)`);

      // Log final timing summary for non-streaming
      console.log(`\n⏱️ [TIMING SUMMARY] Complete request flow:`);
      timingLog.steps.forEach((step, index) => {
        console.log(`   ${index + 1}. ${step.step}: ${step.duration}ms (Total: ${step.totalElapsed}ms)`);
      });
      console.log(`⏱️ [TIMING SUMMARY] Total Duration: ${totalDuration}ms\n`);

      // Add to conversation history
      await cacheService.addConversationEntry(currentSessionId, query, response, {
        documentsUsed: documents.length,
        contextLength: finalContext.length,
        conversationHistoryLength: conversationHistory.length,
        cached: {
          apiKey: userServiceDuration === 0,
          context: vectorSearchDuration === 0
        },
        semanticRefinement: {
          applied: refinementResult.refinements.length > 0,
          refinements: refinementResult.refinements.length,
          processingTime: preprocessDuration,
          originalQuery: query,
          refinedQuery: refinedQuery
        },
        metadata: params.metadata || null
      });

      console.log(`⏱️ [TIMING] Total request completed in: ${totalDuration}ms`);
      console.log(`📤 RESPONSE BEING SENT TO CLIENT`);
      console.log(`📤 ═══════════════════════════════════════════════════════════════`);
      console.log(`📄 Response Type: JSON (Non-streaming)`);
      console.log(`🔢 Session ID: ${currentSessionId}`);
      console.log(`📏 Response Length: ${response.length} characters`);
      console.log(`⏱️ Total Duration: ${totalDuration}ms`);
      console.log(`✅ Status: Success`);
      console.log(`📋 Response Preview: "${response.substring(0, 100)}${response.length > 100 ? '...' : ''}"`);
      console.log(`📤 ═══════════════════════════════════════════════════════════════\n`);
      res.json({
        error: false,
        sessionId: currentSessionId,
        response
      });
    }

  } catch (error) {
    console.error('❌ API v1 chat error:', error.message);

    // Return error in JSON format if headers not sent yet
    if (!res.headersSent) {
      console.log('📤 Sending error response from main handler');
      const statusCode = error.message.includes('validation failed') ? 403 :
        error.message.includes('not found') ? 404 : 500;

      return res.status(statusCode).json({
        error: true,
        message: error.message
      });
    } else {
      console.log('⚠️ Headers already sent, skipping error response from main handler');
    }
  }
}

/**
 * Main API endpoint: /api/v1/chat/
 * ChatAI functionality endpoint
 * 
 * POST Method:
 * Content-Type: application/json
 * Request body supports additional parameters:
 * - model: AI model to use
 * - temperature: Response creativity (0.0-1.0)
 * - maxTokens: Maximum response length
 * - context: Additional context for the query
 * - metadata: Custom metadata object
 * 
 * Request body format:
 * {
 *   "apikey": "your-api-key",
 *   "query": "User's question/query",
 *   "stream": true,
 *   "sessionId": "optional-session-id",
 *   "model": "optional-model-name",
 *   "temperature": 0.7,
 *   "maxTokens": 1000,
 *   "context": "optional-context",
 *   "metadata": { "custom": "data" }
 * }
 * 
 * Authorization can also be provided via Authorization header: Bearer {API_KEY}
 */
router.post('/api/v1/chat/', guardrailsValidation.validateRequest(), async (req, res) => {
  // Extract parameters from request body for POST
  const params = {
    apikey: req.body.apikey || req.headers.authorization?.replace('Bearer ', ''),
    query: req.body.query,
    sessionId: req.body.sessionId,
    stream: req.body.stream !== undefined ? req.body.stream.toString() : 'true',
    testMode: req.body.testMode !== undefined ? req.body.testMode.toString() : 'false',
    // Additional POST-specific parameters
    model: req.body.model,
    temperature: req.body.temperature,
    maxTokens: req.body.maxTokens,
    context: req.body.context,
    metadata: req.body.metadata
  };

  await handleChatRequest(req, res, params);
});

// Test endpoint for vector search integration (without User Service dependency)
router.get('/test-vector', async (req, res) => {
  try {
    const { query = 'test query' } = req.query;
    const vectorSearchService = require('../services/vectorSearchService');

    // Mock documents for testing
    const mockDocuments = [
      {
        id: 1,
        filename: 'test-document.txt',
        parsedText: 'ChatAI Vector Database Integration Test Document. This document contains information about vector databases, semantic search, and ChatAI platform integration. Vector databases enable efficient similarity search using embeddings.',
        appId: 'test-app-123'
      },
      {
        id: 2,
        filename: 'integration-guide.txt',
        parsedText: 'Integration Guide for ChatAI Platform. This guide explains how to integrate vector databases with the ChatAI SDK. The system supports tenant isolation using appId for secure multi-client deployments.',
        appId: 'test-app-123'
      }
    ];

    // console.log(`🧪 Testing vector search with query: "${query}"`);

    // Test vector search service
    const context = await vectorSearchService.retrieveFromMultipleDocuments(
      mockDocuments,
      query,
      'test-app-123'
    );

    // Get vector database stats
    const stats = await vectorSearchService.getStats();

    res.json({
      status: 'success',
      test: 'Vector Search Integration',
      query: query,
      documentsProcessed: mockDocuments.length,
      contextGenerated: {
        length: context.length,
        preview: context.substring(0, 200) + (context.length > 200 ? '...' : '')
      },
      vectorDatabase: {
        status: stats.status || 'not_available',
        stats: stats
      },
      integration: {
        service: 'Qdrant Vector Database',
        fallbackMode: stats.status === 'error',
        tenantIsolation: 'appId-based'
      }
    });

  } catch (error) {
    console.error('❌ Test vector endpoint error:', error.message);
    res.status(500).json({
      status: 'error',
      test: 'Vector Search Integration',
      error: error.message
    });
  }
});

// Health check endpoint
router.get('/health', async (req, res) => {
  try {
    const vectorSearchService = require('../services/vectorSearchService');

    // Check vector database status
    let vectorDbStatus = 'unknown';
    let vectorDbStats = {};

    try {
      vectorDbStats = await vectorSearchService.getStats();
      vectorDbStatus = vectorDbStats.status === 'green' ? 'healthy' : 'degraded';
    } catch (error) {
      vectorDbStatus = 'error';
      vectorDbStats = { error: error.message };
    }

    res.json({
      status: 'healthy',
      timestamp: new Date().toISOString(),
      service: 'ChatAI',
      endpoints: {
        main: 'POST /api/v1/chat/',
        health: '/health',
        apiHealth: '/api/v1/chat/health'
      },
      // vectorDatabase: {
      //   status: vectorDbStatus,
      //   stats: vectorDbStats
      // }
    });
  } catch (error) {
    res.status(500).json({
      status: 'error',
      timestamp: new Date().toISOString(),
      service: 'ChatAI SDK Clean',
      error: error.message
    });
  }
});

// API v1 Health check endpoint
router.get('/api/v1/chat/health', async (req, res) => {
  try {
    const vectorSearchService = require('../services/vectorSearchService');

    // Check vector database status
    let vectorDbStatus = 'unknown';
    let vectorDbStats = {};

    try {
      vectorDbStats = await vectorSearchService.getStats();
      vectorDbStatus = vectorDbStats.status === 'green' ? 'healthy' : 'degraded';
    } catch (error) {
      vectorDbStatus = 'error';
      vectorDbStats = { error: error.message };
    }

    res.json({
      status: 'healthy',
      timestamp: new Date().toISOString(),
      service: 'ChatAI API v1',
      version: '1.0',
      endpoints: {
        main: 'POST /api/v1/chat/',
        health: '/health',
        apiHealth: '/api/v1/chat/health'
      },
      // vectorDatabase: {
      //   status: vectorDbStatus,
      //   stats: vectorDbStats
      // }
    });
  } catch (error) {
    res.status(500).json({
      status: 'error',
      timestamp: new Date().toISOString(),
      service: 'ChatAI SDK Clean API v1',
      version: '1.0',
      error: error.message
    });
  }
});

// AWS Health check endpoint
router.get('/health/aws', async (req, res) => {
  try {
    const configManager = require('../config');
    const { databaseConfigManager } = require('../config/database');

    // Get health status from all AWS-related services
    const [configHealth, databaseHealth] = await Promise.all([
      configManager.getHealthStatus(),
      databaseConfigManager.getHealthStatus()
    ]);

    const overallStatus = configHealth.awsSecretsManager.status === 'healthy' ||
      configHealth.awsSecretsManager.status === 'fallback' ? 'healthy' : 'degraded';

    res.json({
      status: overallStatus,
      timestamp: new Date().toISOString(),
      service: 'ChatAI AWS Integration',
      aws: {
        secretsManager: configHealth.awsSecretsManager,
        configuration: configHealth.configuration,
        database: databaseHealth
      },
      summary: {
        usingSecretsManager: configHealth.awsSecretsManager.configured,
        credentialsAvailable: configHealth.awsSecretsManager.status !== 'error',
        fallbackMode: configHealth.awsSecretsManager.status === 'fallback'
      }
    });
  } catch (error) {
    res.status(500).json({
      status: 'error',
      timestamp: new Date().toISOString(),
      service: 'ChatAI AWS Integration',
      error: error.message
    });
  }
});

// Mount vector processing routes
router.use('/api/vector', vectorProcessingRoutes);

module.exports = router;
